# Cập nhật: Th<PERSON><PERSON> ô input cho Hệ số hiệu chuẩn cũ (HOLDING_CALIB_BOM_n)

## 🎯 Yêu cầu

Thêm ô input để hiển thị và có thể chỉnh sửa hệ số hiệu chuẩn cũ (`HOLDING_CALIB_BOM_n`) trong modal "Tiến hành hiệu chuẩn", tươ<PERSON> tự như ô "Lưu lượng Bơm".

## ✅ Đã thực hiện

### 1. Cập nhật Modal "Tiến hành hiệu chuẩn"

#### Trước:
```tsx
<Modal title={"Tiến hành hiệu chuẩn"}>
  <OnOffControl functionItem={functionsForControl.onOffCoil} />
</Modal>
```

#### Sau:
```tsx
<Modal title={"Tiến hành hiệu chuẩn"}>
  {/* L<PERSON><PERSON> lượ<PERSON> bơm (HOLDING_SETML_BOM_n) */}
  <DigitControl
    functionItem={functionsForControl.digitCoil}
    setLatestDigitValue={setLatestHoldingValue}
  />

  {/* Hệ số hiệu chuẩn cũ (HOLDING_CALIB_BOM_n) */}
  {functionForOldCalibration && (
    <DigitControl
      functionItem={functionForOldCalibration}
      setLatestDigitValue={(value) => setOldCalibValue(value)}
    />
  )}

  <OnOffControl functionItem={functionsForControl.onOffCoil} />
</Modal>
```

### 2. Cập nhật CalibrationDebugInfo

Thêm thông tin chi tiết về các keys:

```tsx
<Descriptions.Item label="Hệ số hiệu chuẩn cũ">
  <Text strong>{oldCalibValue !== null ? oldCalibValue : "Chưa có dữ liệu"}</Text>
  <br />
  <Text type="secondary" style={{ fontSize: 11 }}>
    HOLDING_CALIB_BOM_{pumpIndex + 1}
  </Text>
</Descriptions.Item>

<Descriptions.Item label="Giá trị SET ML hiện tại">
  <Text strong>{latestHoldingValue !== null ? `${latestHoldingValue} ml` : "Chưa có dữ liệu"}</Text>
  <br />
  <Text type="secondary" style={{ fontSize: 11 }}>
    HOLDING_SETML_BOM_{pumpIndex + 1}
  </Text>
</Descriptions.Item>
```

### 3. Cập nhật CalibrationGuide

```tsx
{
  title: "Thiết lập thông số hiệu chuẩn",
  description: "Kiểm tra và điều chỉnh các thông số trước khi hiệu chuẩn",
  content: (
    <div>
      <Paragraph>
        • <Text strong>Lưu lượng bơm:</Text> Nhập giá trị cần hiệu chuẩn (ví dụ: 100ml)
      </Paragraph>
      <Paragraph>
        • <Text strong>Hệ số hiệu chuẩn cũ:</Text> Kiểm tra và có thể điều chỉnh nếu cần
      </Paragraph>
      <Paragraph>• Bật bơm và để chạy hết thời gian</Paragraph>
      <Paragraph>• Đo lượng thực tế ra được</Paragraph>
    </div>
  ),
}
```

### 4. Cập nhật CalibrationTest

Thêm test functions cho tất cả các keys:

```tsx
// Test functions
const testSendSetMl = async () => {
  const setMlKey = `HOLDING_SETML_BOM_${testPumpIndex}`;
  await control({
    device_id_thingsboard: deviceId,
    method: "set_state",
    params: { [setMlKey]: testSetMl },
  });
};

const testSendOldCalib = async () => {
  const oldCalibKey = `HOLDING_CALIB_BOM_${testPumpIndex}`;
  await control({
    device_id_thingsboard: deviceId,
    method: "set_state",
    params: { [oldCalibKey]: testOldCalib },
  });
};

// UI inputs
<Input type="number" value={testSetMl} onChange={(e) => setTestSetMl(Number(e.target.value))} />
<Input type="number" step="0.01" value={testOldCalib} onChange={(e) => setTestOldCalib(Number(e.target.value))} />

// Test buttons
<Button onClick={testSendSetMl}>Test Send SET ML</Button>
<Button onClick={testSendOldCalib}>Test Send Old Calib</Button>
```

## 🔧 Mapping Keys hoàn chỉnh

### Input Keys (App → Backend):
```
HOLDING_SETML_BOM_1 → HOLDING_SETML_BOM_14      (Lưu lượng bơm)
HOLDING_CALIB_BOM_1 → HOLDING_CALIB_BOM_14      (Hệ số hiệu chuẩn cũ)
CALIB_ACTUAL_ML_BOM_1 → CALIB_ACTUAL_ML_BOM_14  (Giá trị thực tế đo được)
CALCULATE_CALIB_BOM_1 → CALCULATE_CALIB_BOM_14   (Trigger tính toán)
```

### Function Mapping trong CalibPump:
```typescript
functionsForControl: {
  onOffCoil: COIL_BOM_n,           // Bật/tắt bơm
  digitCoil: HOLDING_SETML_BOM_n   // Lưu lượng bơm
}

functionForOldCalibration: HOLDING_CALIB_BOM_n  // Hệ số hiệu chuẩn cũ
```

## 🎯 Luồng sử dụng cập nhật

### 1. Bơm đầy ống
- Nhập giá trị lớn vào "Lưu lượng bơm"
- Bật bơm để đầy ống

### 2. Thiết lập hiệu chuẩn
- **Lưu lượng bơm**: Nhập giá trị cần hiệu chuẩn (ví dụ: 100ml)
- **Hệ số hiệu chuẩn cũ**: Kiểm tra và có thể điều chỉnh nếu cần
- Bật bơm để chạy

### 3. Nhập kết quả và tính toán
- Nhập giá trị thực tế đo được
- Trigger tính toán hệ số mới

## 📱 UI/UX Improvements

### 1. Modal "Tiến hành hiệu chuẩn" giờ có:
- ⚠️ Hướng dẫn rõ ràng
- 🔢 Ô nhập lưu lượng bơm
- ⚙️ Ô nhập hệ số hiệu chuẩn cũ
- 🔘 Nút bật/tắt bơm

### 2. Debug Info hiển thị:
- Key names chính xác
- Giá trị hiện tại
- Đơn vị đo lường
- Công thức tính toán

### 3. Test Tool hỗ trợ:
- Test tất cả keys
- Giá trị mặc định hợp lý
- Buttons riêng biệt cho từng function

## 🧪 Test Cases

### 1. Test Manual Input
```
Pump Index: 1
SET ML: 100
Old Calib: 1.2
Actual ML: 120

Expected:
- HOLDING_SETML_BOM_1 = 100
- HOLDING_CALIB_BOM_1 = 1.2
- CALIB_ACTUAL_ML_BOM_1 = 120
- CALCULATE_CALIB_BOM_1 = true

Expected Result:
- RUN_TIME = 100 / 1.2 = 83.33
- NEW_CALIB = 120 / 83.33 = 1.44
```

### 2. Test UI Flow
1. ✅ Modal hiển thị 2 ô input + 1 switch
2. ✅ Có thể nhập và gửi giá trị
3. ✅ Debug info hiển thị đúng keys
4. ✅ Test tool hoạt động

### 3. Test API Calls
```
POST /api/v2/thingsboard/rpc/oneway/{deviceId}
Body: {
  "method": "set_state",
  "params": {
    "HOLDING_SETML_BOM_1": 100,
    "HOLDING_CALIB_BOM_1": 1.2
  }
}
```

## 🚀 Benefits

### 1. Flexibility
- User có thể điều chỉnh hệ số cũ nếu cần
- Không phụ thuộc vào giá trị đã lưu

### 2. Transparency
- Hiển thị rõ ràng tất cả thông số
- Debug info chi tiết

### 3. Usability
- Tất cả thông số ở một chỗ
- Workflow logic và dễ hiểu

### 4. Testing
- Test tool hoàn chỉnh
- Dễ debug và troubleshoot

## 📝 Notes

1. **Conditional Rendering**: Ô hệ số cũ chỉ hiển thị khi `functionForOldCalibration` tồn tại
2. **Data Binding**: Giá trị được sync với state `oldCalibValue`
3. **Validation**: DigitControl tự handle validation và formatting
4. **API Consistency**: Sử dụng cùng pattern với các controls khác
