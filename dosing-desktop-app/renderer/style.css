/* Hello developers of VIIS */
/* For basic purposes, this is a CSS file for the renderer process. */
/* This project is running on an embedded-device based on Linux, so the resources need to be optimized */

/* There are all className contain the concepts for elements */
/* 1. center-screen:    center the content both horizontal and vertical */
/* 2. flex:             flex layout */

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.center-middle-screen {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-top-screen {
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hoverable {
  cursor: pointer;
}

.hoverable:hover {
  opacity: 0.5;
}

.hoverable:focus {
  opacity: 0.5;
}

.hoverable:active {
  opacity: 0.2;
}

@keyframes animate-wave {
  to {
    background-position: 200% center;
  }
}

.route-pipeline {
  font-weight: bold;
  margin: 0;
  background-image: linear-gradient(
    90deg,
    #1200dc 0%,
    #c0d9ec 25%,
    #3350dd 75%,
    #1200dc 100%
  );
  background-size: auto auto;
  background-clip: border-box;
  background-size: 200% auto;
  box-shadow: 0px 0px 20px -1px #0043e1;
  color: #ffffff;
  /* background-clip: text; */
  /* -webkit-background-clip: text; */
  /* -webkit-text-fill-color: transparent; */
  animation: animate-wave 5s linear infinite;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Hide scrollbars globally */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
