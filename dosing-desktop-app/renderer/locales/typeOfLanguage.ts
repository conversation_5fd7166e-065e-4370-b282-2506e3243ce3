export type TypeOfLanguage = {
  "common.login.success": string;
  "common.login.error": string;
  "common.logout.success": string;
  "common.calibsensors.select_input_type": string;
  "common.calibsensors.input.card": string;
  "common.calibsensors.input.from_user": string;
  "common.calibsensors.input.from_device": string;
  "common.calibsensors.visualization.card": string;
  "common.calibsensors.table.card": string;
  "common.control.switch.on": string;
  "common.control.switch.off": string;
  "common.control.post.success": string;
  "common.control.post.error": string;
  "common.control.tab.config.config": string;
};
