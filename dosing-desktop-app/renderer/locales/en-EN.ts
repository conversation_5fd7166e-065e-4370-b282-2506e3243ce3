import { TypeOfLanguage } from "./typeOfLanguage";

export const en_EN: TypeOfLanguage = {
  "common.login.success": "Login successful!",
  "common.login.error": "Login failed!",
  "common.logout.success": "Logout successful!",
  "common.calibsensors.select_input_type": "Select input type",
  "common.calibsensors.input.card": "Input",
  "common.calibsensors.input.from_user": "From user",
  "common.calibsensors.input.from_device": "From device",
  "common.calibsensors.visualization.card": "Visualization",
  "common.calibsensors.table.card": "Table",
  "common.control.switch.on": "ON",
  "common.control.switch.off": "OFF",
  "common.control.post.success": "Control successful!",
  "common.control.post.error": "Control failed!",
  "common.control.tab.config.config": "Config",
};
