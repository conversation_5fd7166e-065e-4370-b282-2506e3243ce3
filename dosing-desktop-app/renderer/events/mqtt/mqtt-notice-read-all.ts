import { genNoticeReadAllTopic } from "../../utils/mqtt";
import EventEmitter from "eventemitter3";
import { OnMessageCallback } from "mqtt/types/lib/client";
import { HandleRealtimeMqtt, NoticeDataRes } from "./type";

export type MqttNoticeDataSub = NoticeDataRes;

export class MqttNoticeEventReadAllControl
  implements HandleRealtimeMqtt<MqttNoticeDataSub>
{
  events: EventEmitter;
  keyEvent: string;

  constructor() {
    this.events = new EventEmitter();
    this.keyEvent = "notice:read-all"; // Replace with appropriate value
  }
  handleReceiveMessage: OnMessageCallback = (topic, payload) => {
    try {
      if (topic === genNoticeReadAllTopic()) {
        this.emit(JSON.parse(payload.toString()));
      }
    } catch (error) {}
  };
  emit(message: MqttNoticeDataSub) {
    this.events.emit(this.keyEvent, message);
  }

  on(handle: (message: MqttNoticeDataSub) => void) {
    const removeSubscribe = () => {
      this.events.removeListener(this.keyEvent, handle);
    };

    this.events.on(this.keyEvent, handle);

    return {
      removeSubscribe,
    };
  }

  removeAllListeners() {
    this.events.removeAllListeners("message");
  }
}
