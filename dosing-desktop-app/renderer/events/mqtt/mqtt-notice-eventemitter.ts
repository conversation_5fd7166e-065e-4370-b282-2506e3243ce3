// import { NoticeDataRes } from "@/services/web-notification";
import {
  genNoticeSubTopicCustomerId,
  genNoticeSubTopicUserId,
  genNoticeUpdateTopic,
} from "../../utils/mqtt";
import EventEmitter from "eventemitter3";
import { OnMessageCallback } from "mqtt/types/lib/client";
import { HandleRealtimeMqtt, NoticeDataRes } from "./type";

export type MqttNoticeDataSub = NoticeDataRes;

export class MqttNoticeEventEmitterControl
  implements HandleRealtimeMqtt<MqttNoticeDataSub>
{
  events: EventEmitter;
  keyEvent: string;

  constructor() {
    this.events = new EventEmitter();
    this.keyEvent = "message"; // Replace with appropriate value
  }
  handleReceiveMessage: OnMessageCallback = (topic, payload) => {
    try {
      const accessTopics = [
        genNoticeSubTopicUserId(),
        genNoticeUpdateTopic(),
        genNoticeSubTopicCustomerId(),
      ];
      if (accessTopics.includes(topic) && payload) {
        this.emit(JSON.parse(payload.toString()));
      }
    } catch (error) {}
  };
  emit(message: MqttNoticeDataSub) {
    this.events.emit(this.keyEvent, message);
  }

  on(handle: (message: MqttNoticeDataSub) => void) {
    const removeSubscribe = () => {
      this.events.removeListener(this.keyEvent, handle);
    };

    this.events.on(this.keyEvent, handle);

    return {
      removeSubscribe,
    };
  }

  removeAllListeners() {
    this.events.removeAllListeners("message");
  }
}
