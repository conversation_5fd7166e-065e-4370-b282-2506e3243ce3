import dayjs from "dayjs";
import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { ProgramExecutionHistoryType } from "../services/program-execution-history";

interface ProgramExecutionHistoryState {
  startTime: string;
  endTime: string;
  setStartTime: (startTime: string) => void;
  setEndTime: (endTime: string) => void;
  programExecutionHistory: ProgramExecutionHistoryType[];
  setProgramExecutionHistory: (
    programExecutionHistory: ProgramExecutionHistoryType[]
  ) => void;
}

const useProgramExecutionHistoryStore = create(
  immer<ProgramExecutionHistoryState>((set, get) => ({
    startTime: dayjs().subtract(7, "day").format("YYYY-MM-DD HH:mm:ss"),
    endTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    setStartTime: (startTime: string) => set({ startTime }),
    setEndTime: (endTime: string) => set({ endTime }),
    programExecutionHistory: [],
    setProgramExecutionHistory: (programExecutionHistory: any[]) =>
      set({ programExecutionHistory }),
  }))
);
export default useProgramExecutionHistoryStore;
