import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { vi_VN } from "../locales/vi-VN";
import { en_EN } from "../locales/en-EN";
import { TypeOfLanguage } from "../locales/typeOfLanguage";

export type languageOptions = "vi-VN" | "en-US";

interface LanguageState {
  language: languageOptions;
  languageData: TypeOfLanguage;
  setLanguage: (language: languageOptions) => void;
}

const useLanguageStore = create(
  immer<LanguageState>((set, get) => ({
    language: "vi-VN",
    languageData: vi_VN,
    setLanguage: (language: languageOptions) => {
      set({ language: language });
      set({ languageData: language === "vi-VN" ? vi_VN : en_EN });
    },
  }))
);
export default useLanguageStore;
