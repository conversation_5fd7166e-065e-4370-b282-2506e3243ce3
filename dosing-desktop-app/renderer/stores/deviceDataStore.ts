import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  FunctionList,
  I_IOTDevice,
  deviceInProjectList,
} from "../services/device/devices";
// import { deviceSharedInProjectList } from '@/services/sharing-device';
import { DOCTYPE_ERP } from "../constant/constant";
// import { dayjsUtil } from '@/utils/date';
import { useDeviceOnlineChange } from "../services/device/useDeviceOnlineChange";
import { Dayjs } from "dayjs";

const now = new Date();
// Get date from one month ago
const oneMonthAgo = new Date();
oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
const defaultStartTime = oneMonthAgo.getTime();
const defaultEndTime = now.getTime();
let timeRangeRef: [number, number] = [defaultStartTime, defaultEndTime];

export interface CalibrationInformationProps {
  fulfill: boolean;
  calibration: boolean;
  startTimestampCalibration: Dayjs;
  totalTimeCalibration: number;
  isPumpActivedBefore: boolean;
}

interface DeviceDataState {
  deviceData?: I_IOTDevice;
  setDeviceData: (deviceData: I_IOTDevice) => void;
  loadingDataDevice: boolean;
  deviceId: string;
  setDeviceId: (deviceId: string) => void;
  isOnline: boolean;
  setIsOnline: (isOnline: boolean) => void;
  functionListForMonitor: any[];
  setFunctionListForMonitor: (functionListForMonitor: any[]) => void;
  functionListForControl: any[];
  setFunctionListForControl: (functionListForControl: any[]) => void;
  functionListForCalibration: any[];
  setFunctionListForCalibration: (functionListForCalibration: any[]) => void;
  calibrationInformation: CalibrationInformationProps[];
  setCalibrationInformation: React.Dispatch<
    React.SetStateAction<CalibrationInformationProps[]>
  >;

  setTimeRef: (newTimeRange: [number, number]) => void;
  getTimeRange: () => [number, number];
  // fetchDataDevice: (deviceId: string) => Promise<void>;
}

const useDeviceDataStore = create(
  immer<DeviceDataState>((set, get) => ({
    deviceData: undefined,
    setDeviceData: (deviceData: I_IOTDevice) => set({ deviceData: deviceData }),
    loadingDataDevice: false,
    deviceId: "",
    setDeviceId: (deviceId: string) => set({ deviceId: deviceId }),
    isOnline: false,
    setIsOnline: (isOnline: boolean) => set({ isOnline: isOnline }),
    functionListForMonitor: [],
    setFunctionListForMonitor: (functionListForMonitor: any[]) =>
      set({ functionListForMonitor: functionListForMonitor }),
    setTimeRef: (newTimeRange) => {
      timeRangeRef = newTimeRange;
    },
    getTimeRange: () => {
      return timeRangeRef;
    },
    functionListForControl: [],
    setFunctionListForControl: (functionListForControl: any[]) =>
      set({ functionListForControl: functionListForControl }),
    functionListForCalibration: [],
    setFunctionListForCalibration: (functionListForCalibration: any[]) =>
      set({ functionListForCalibration: functionListForCalibration }),
    calibrationInformation: [],
    setCalibrationInformation: (calibrationInformation: any[]) =>
      set({ calibrationInformation: calibrationInformation }),
  }))
);
export default useDeviceDataStore;
