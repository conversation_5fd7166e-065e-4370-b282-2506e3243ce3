import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { FunctionList } from "../services/device/devices";

export interface ScheduleAction {
  [key: string]: string | number | boolean | null | undefined;
}

export interface ScheduleProgram {
  name?: string;
  label?: string;
  id?: string;
  device_id: string;
  status: string;
  action: ScheduleAction;
  enable: number;
  set_time: string | null;
  start_date: string;
  end_date: string;
  type: string;
  interval: string;
  start_time: string;
  end_time: string;
  is_from_local: number;
  is_synced: number;
  schedule_plan_id: string;
  is_deleted: number;
  template_id: string | null;
  creation: string;
  modified: string;
}

export interface SchedulePlanProps {
  creation?: string;
  customer_id: string;
  device_id: string;
  enable: number;
  end_date: string;
  is_deleted: number;
  is_from_local: number;
  is_synced: number;
  label: string;
  modified?: string;
  name?: string;
  schedule_count: number;
  schedules: ScheduleProgram[];
  start_date: string;
  status: string;
}

interface SchedulePlanState {
  schedulePlans: SchedulePlanProps[];
  setSchedulePlans: (schedulePlans: SchedulePlanProps[]) => void;
  scheduleProgramTriggerImmediately: FunctionList | null;
  setScheduleProgramTriggerImmediately: (program: FunctionList | null) => void;
}

const useSchedulePlanStore = create(
  immer<SchedulePlanState>((set, get) => ({
    schedulePlans: [],
    setSchedulePlans: (schedulePlans: SchedulePlanProps[]) =>
      set({ schedulePlans: schedulePlans }),
    scheduleProgramTriggerImmediately: null,
    setScheduleProgramTriggerImmediately: (program: FunctionList | null) =>
      set({ scheduleProgramTriggerImmediately: program }),
  }))
);
export default useSchedulePlanStore;
