import { FC, useEffect } from "react";
import { Row, Col, Drawer, message, Button } from "antd";
import { useState } from "react";
import dayjs from "dayjs";
import DetailedProgram from "./Detail/DetailedProgram";
import { ProgramRunningFromBroker } from "../../../stores/programRunningStore";
import { RightOutlined } from "@ant-design/icons";
import useSchedulePlanStore, {
  SchedulePlanProps,
  ScheduleProgram,
} from "../../../stores/schedulePlanStore";

const ProgramRunning: FC<ProgramRunningFromBroker> = ({
  scheduleId,
  label,
  status,
  start_time,
  end_time,
  timestamp,
}) => {
  const { schedulePlans } = useSchedulePlanStore();

  const [program, setProgram] = useState<ScheduleProgram | null>(null);

  useEffect(() => {
    if (schedulePlans.length === 0) return;

    schedulePlans.forEach((plan: SchedulePlanProps) => {
      plan.schedules.forEach((program) => {
        if (program.id === scheduleId) setProgram(program);
      });
    });
  }, [schedulePlans]);

  const [openDrawerToEditProgram, setOpenDrawerToEditProgram] =
    useState<boolean>(false);
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        boxShadow: "0px 0px 8px rgba(0, 0, 0, 0.1)",
        border: "1px solid #eee",
        padding: 20,
        borderRadius: 16,
        background:
          "linear-gradient(to bottom left,rgb(41, 136, 237), 50%, rgb(69, 151, 99))",
        minHeight: 100,
        overflow: "hidden",
        position: "relative",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: 8,
            flexDirection: "column",
            alignItems: "start",
            justifyContent: "space-between",
          }}
        >
          <p
            style={{
              fontSize: 18,
              fontWeight: "bold",
              margin: 0,
              color: "white",
              cursor: "pointer",
            }}
          >
            {label}
          </p>
          <p
            style={{
              margin: 0,
              color: "white",
              fontSize: 14,
            }}
          >
            Thời điểm:&nbsp;&nbsp;&nbsp;
            <strong>
              {dayjs(start_time, "HH:mm:ss").format("HH:mm:ss")} -{" "}
              {dayjs(end_time, "HH:mm:ss").format("HH:mm:ss")}
            </strong>
          </p>
          <div
            style={{
              marginTop: 16,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
            }}
          >
            <div
              style={{
                height: 8,
                width: 8,
                borderRadius: "50%",
                backgroundColor: "rgb(54, 221, 65)",
              }}
            ></div>
            <p
              style={{
                fontSize: 14,
                fontWeight: "bold",
                margin: 0,
                color: "rgb(85, 232, 95)",
                cursor: "pointer",
              }}
            >
              {status === "running" ? "Đang hoạt động" : "..."}
            </p>
          </div>
        </div>

        <Button
          type="default"
          style={{ borderRadius: 4, zIndex: 10 }}
          size="large"
          color="geekblue"
          icon={<RightOutlined />}
          onClick={() => setOpenDrawerToEditProgram(true)}
        ></Button>
      </div>

      <p
        style={{
          margin: 0,
          position: "absolute",
          right: 0,
          top: 0,
          fontSize: 150,
          color: "white",
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        {" "}
        &gt; &gt; &gt;
      </p>

      <Drawer
        title="Chỉnh sửa chương trình"
        open={openDrawerToEditProgram}
        onClose={() => {
          setOpenDrawerToEditProgram(false);
        }}
        width={"70%"}
      >
        <DetailedProgram
          program={program}
          onClose={() => {
            setOpenDrawerToEditProgram(false);
          }}
          start_date_of_plan={program?.start_date}
          end_date_of_plan={program?.end_date}
        />
      </Drawer>
    </div>
  );
};
export default ProgramRunning;
