import {
  But<PERSON>,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Switch,
  TimePicker,
} from "antd";
import { FC, useEffect, useMemo, useState } from "react";
import useDeviceDataStore from "../../../../stores/deviceDataStore";
import { FunctionList } from "../../../../services/device/devices";
import { generateAPIPath } from "../../../../services/utilities";
import { DashboardOutlined } from "@ant-design/icons";
import { createScheduleProgram } from "../../../../services/schedule";
import useSchedulePlanStore, {
  ScheduleAction,
} from "../../../../stores/schedulePlanStore";
import dayjs from "dayjs";
import InputTextWithKeyboard from "../../../../components/virtual-input/InputTextWithKeyboard";
import InputNumberWithKeyboard from "../../../../components/virtual-input/InputNumberWithKeyboard";

interface CreateProgramProps {
  onClose: () => void;
  deviceId: string;
  schedulePlanId: string;
  start_date: string;
  end_date: string;
}

const CreateProgram: FC<CreateProgramProps> = ({
  onClose,
  deviceId,
  schedulePlanId,
  start_date,
  end_date,
}) => {
  const [form] = Form.useForm();
  const { functionListForControl } = useDeviceDataStore();
  const { schedulePlans, setSchedulePlans, scheduleProgramTriggerImmediately } =
    useSchedulePlanStore();

  const [intervalDays, setIntervalDays] = useState([
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
  ]);

  const [dates, setDates] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs(start_date),
    dayjs(end_date),
  ]);

  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    []
  );
  useEffect(() => {
    if (!scheduleProgramTriggerImmediately) return;
    setOptions(
      scheduleProgramTriggerImmediately.enum_value.split(",").map((item) => ({
        value: item.trim(),
        label: item.trim(),
      }))
    );
  }, [scheduleProgramTriggerImmediately]);

  const onFinish = async (values: any) => {
    try {
      const action: ScheduleAction = Object.fromEntries(
        Object.entries(values.action || {}).map(([key, value]) => {
          if (typeof value === "boolean") {
            return [key, String(value)];
          } else if (typeof value === "number" || typeof value === "string") {
            return [key, value];
          } else {
            return [key, String(value)];
          }
        })
      );

      // Set default values if not provided
      const startTime =
        values.start_time || dayjs().hour(8).minute(0).second(0);
      const timeRunning = values.time_running || 60; // default 60 seconds
      const interval = values.interval || intervalDays;

      const programToPush = {
        name: values.name,
        start_time: startTime.format("HH:mm:ss"),
        end_time: startTime.add(timeRunning, "seconds").format("HH:mm:ss"),
        start_date: dates[0].format("YYYY-MM-DD"),
        end_date: dates[1].format("YYYY-MM-DD"),
        interval: interval.join(","),
        enable: 1,
        schedule_plan_id: schedulePlanId,
        device_id: deviceId,
        type: "",
        action: action,
      };
      console.log("programToPush: ", programToPush);
      const res = await createScheduleProgram(programToPush);
      if (res?.statusOK) {
        message.success("Tạo chương trình thành công");
        const updatedPlans = [...schedulePlans];
        updatedPlans
          .find((plan) => plan.name === schedulePlanId)
          ?.schedules.push(res?.responseData?.result?.data);
        setSchedulePlans(updatedPlans);
        form.resetFields();
        onClose();
      }
    } catch (error) {
      console.log("Error: ", error);
      message.error("Vui lòng nhập đầy đủ thông tin");
    }
  };

  return (
    <Form layout="vertical" form={form} style={{ width: "100%" }}>
      <div
        style={{
          zIndex: 100,
          position: "fixed",
          bottom: 24,
          right: 24,
          display: "flex",
          justifyContent: "flex-end",
          gap: 8,
          padding: 8,
          background: "rgba(255, 255, 255, 0.5)",
          borderRadius: 8,
          backdropFilter: "blur(5px)",
          border: "1px solid #ddd",
          boxShadow: "0px 0px 50px 2px rgba(0, 0, 0, 0.25)",
        }}
      >
        <Button onClick={() => onClose()}>Hủy</Button>
        <Button type="primary" onClick={() => onFinish(form.getFieldsValue())}>
          Lưu
        </Button>
      </div>

      {/* <Form.Item
        name="interval"
        label="Áp dụng cho các thứ"
        initialValue={intervalDays}
      >
        <Checkbox.Group
          options={[
            { label: "Chủ Nhật", value: "0" },
            { label: "Thứ 2", value: "1" },
            { label: "Thứ 3", value: "2" },
            { label: "Thứ 4", value: "3" },
            { label: "Thứ 5", value: "4" },
            { label: "Thứ 6", value: "5" },
            { label: "Thứ 7", value: "6" },
          ]}
          value={intervalDays}
          onChange={(e) => setIntervalDays(e)}
        />
      </Form.Item> */}

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Form.Item
            name="name"
            label="Tên chương trình"
            rules={[{ required: true }]}
            layout="vertical"
          >
            {/* <Input style={{ width: "100%" }} /> */}
            <InputTextWithKeyboard style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row>

      {/* <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item
            name="start_time"
            label="Thời gian bắt đầu"
            initialValue={dayjs().hour(8).minute(0).second(0)}
            layout="vertical"
          >
            <TimePicker style={{ width: "100%" }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="time_running"
            label="Thời gian thực hiện (Giây)"
            initialValue={60}
            layout="vertical"
          >
            <InputNumberWithKeyboard style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row> */}

      {/* <Row gutter={[16, 16]}>
        <Col span={24}>
          <Form.Item
            name="date_range"
            label="Ngày thực hiện"
            initialValue={dates}
          >
            <DatePicker.RangePicker
              style={{ width: "100%" }}
              key="date_range_picker"
              value={dates}
              onChange={(values) =>
                setDates(values as [dayjs.Dayjs, dayjs.Dayjs])
              }
              disabledDate={(current) => {
                // Disable dates that are not within the selectedPlan's start and end dates
                const today = dayjs().startOf("day");
                const startDate = dayjs(start_date);
                const exactStartDate = today.isBefore(startDate)
                  ? startDate
                  : today;
                const endDate = dayjs(end_date);

                return (
                  current && (current < exactStartDate || current > endDate)
                );
              }}
            />
          </Form.Item>
        </Col>
      </Row> */}

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item
            name={["action", "env_enum"]}
            rules={[{ required: true }]}
            label="Mã môi trường"
          >
            <Select
              placeholder="Chọn mã môi trường"
              style={{ width: "100%" }}
              options={options}
            />
          </Form.Item>
        </Col>
      </Row>

      <Col span={24} style={{ marginTop: 32 }}>
        {functionListForControl
          .find((fn) => fn.identifier === "tb1")
          ?.children?.map((functionItem) =>
            functionItem.children.length === 0 ? null : (
              <Row key={functionItem.label} style={{ marginBottom: 32 }}>
                <p style={{ margin: 0, fontSize: 16, fontWeight: "bold" }}>
                  {functionItem.label}
                </p>
                <Col span={24} style={{ marginTop: 8 }}>
                  {functionItem?.children?.map(
                    (functionItemChild: FunctionList) => (
                      <Row
                        gutter={[16, 16]}
                        style={{ borderTop: "1px solid #ddd" }}
                        key={functionItemChild.identifier}
                      >
                        <Col span={24}>
                          <Form.Item
                            style={{ marginBottom: 0 }}
                            name={["action", functionItemChild.identifier]}
                            initialValue={
                              functionItemChild.data_type === "Bool" ? false : 0
                            }
                            layout="horizontal"
                            labelCol={{
                              span: 12,
                              style: { textAlign: "left" },
                            }}
                            wrapperCol={{
                              span: 12,
                              style: { textAlign: "right" },
                            }}
                            label={
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  alignItems: "center",
                                }}
                              >
                                {functionItemChild.icon_url ? (
                                  <img
                                    height={"24px"}
                                    src={generateAPIPath(
                                      "api/v2/file/download?file_url=" +
                                        functionItemChild.icon_url
                                    )}
                                    onError={() => <DashboardOutlined />}
                                  />
                                ) : (
                                  <DashboardOutlined />
                                )}
                                <p style={{ margin: 0, marginLeft: 8 }}>
                                  {functionItemChild.label}
                                </p>
                              </div>
                            }
                          >
                            {functionItemChild.data_type === "Bool" && (
                              <Switch style={{ width: 40 }} />
                            )}
                            {functionItemChild.data_type === "Value" && (
                              // <InputNumber
                              //   style={{
                              //     width: 200,
                              //     marginTop: 4,
                              //     marginBottom: 4,
                              //   }}
                              // />
                              <InputNumberWithKeyboard
                                style={{
                                  width: 200,
                                  marginTop: 4,
                                  marginBottom: 4,
                                }}
                              />
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                    )
                  )}
                </Col>
              </Row>
            )
          )}
      </Col>

      <div style={{ height: 80 }}></div>
    </Form>
  );
};

export default CreateProgram;
