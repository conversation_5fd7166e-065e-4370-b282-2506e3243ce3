import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Divider,
  Form,
  Input,
  message,
  Row,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { createSchedulePlan } from "../../../../services/schedule";
import useDeviceDataStore from "../../../../stores/deviceDataStore";
import useSchedulePlanStore from "../../../../stores/schedulePlanStore";
import dayjs from "dayjs";
import InputTextWithKeyboard from "../../../../components/virtual-input/InputTextWithKeyboard";

interface CreateSchedulePlanProps {
  onClose: () => void;
}

const CreateSchedulePlan: FC<CreateSchedulePlanProps> = ({ onClose }) => {
  const [form] = Form.useForm();

  const { deviceId } = useDeviceDataStore();

  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();

  const onFinish = async (values: any) => {
    const startDate = dayjs(values.start_date)
      .startOf("day")
      .format("YYYY-MM-DD");
    const endDate = dayjs(values.end_date).endOf("day").format("YYYY-MM-DD");

    const dataToUpdate = {
      label: values.label,
      device_id: deviceId,
      start_date: startDate,
      end_date: endDate,
      // enable: values.enable ? 1 : 0,
      enable: 0,
    };
    const res = await createSchedulePlan(dataToUpdate);
    if (res?.statusOK) {
      const updatedPlans = [...schedulePlans];
      updatedPlans.push(res?.responseData?.result?.data);
      setSchedulePlans(updatedPlans);
      message.success("Tạo kế hoạch thành công");
      form.resetFields();
      console.log(values);
      onClose();
    }
  };

  return (
    <Form form={form} style={{ width: "100%" }}>
      <div
        style={{
          zIndex: 100,
          position: "fixed",
          bottom: 24,
          right: 24,
          display: "flex",
          justifyContent: "flex-end",
          gap: 8,
          padding: 8,
          background: "rgba(255, 255, 255, 0.5)",
          borderRadius: 8,
          backdropFilter: "blur(5px)",
          border: "1px solid #ddd",
          boxShadow: "0px 0px 50px 2px rgba(0, 0, 0, 0.25)",
        }}
      >
        <Button onClick={() => onClose()}>Hủy</Button>
        <Button type="primary" onClick={() => onFinish(form.getFieldsValue())}>
          Lưu
        </Button>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Form.Item
            name="label"
            label="Tên kế hoạch"
            rules={[{ required: true }]}
            layout="vertical"
          >
            {/* <Input required style={{ width: "100%" }} /> */}
            <InputTextWithKeyboard style={{ width: "100%" }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col span={12}>
          <Form.Item
            name="start_date"
            label="Ngày bắt đầu"
            rules={[{ required: true }]}
            layout="vertical"
          >
            <DatePicker style={{ width: "100%" }} placeholder="" />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item
            name="end_date"
            label="Ngày kết thúc"
            rules={[{ required: true }]}
            layout="vertical"
          >
            <DatePicker style={{ width: "100%" }} placeholder="" />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default CreateSchedulePlan;
