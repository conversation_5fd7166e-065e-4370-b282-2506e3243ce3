import { FC, useState } from "react";
import { SchedulePlanProps } from "../../../stores/schedulePlanStore";
import { <PERSON><PERSON>, <PERSON>er, Switch } from "antd";
import { RightOutlined } from "@ant-design/icons";
import DetailedSchedulePlan from "./Detail/DetailedSchedulePlan";
import EnableSchedulePlan from "./Update/EnableSchedulePlan";

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString("vi-VN", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

const SchedulePlanContainer: FC<SchedulePlanProps> = (plan) => {
  const [openDetailedSchedulePlanDrawer, setOpenDetailedSchedulePlanDrawer] =
    useState(false);
  const [planActivated, setPlanActivated] = useState<boolean>(
    plan.enable !== 0 ? true : false
  );

  return (
    <div
      key={plan.name}
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "start",
        gap: 8,
        border: "1px solid #ddd",
        padding: 16,
        borderRadius: 16,
        background: planActivated ? "rgb(224, 255, 226)" : "#fff",
        width: "100%",
      }}
    >
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          gap: 8,
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 8,
          }}
        >
          <p
            key={plan.name}
            style={{
              fontSize: 16,
              fontWeight: "bold",
              margin: 0,
            }}
          >
            {plan.label}
          </p>
          <p style={{ color: "gray", fontSize: 14, margin: 0 }}>
            Thời gian thực hiện: {formatDate(plan.start_date)} -{" "}
            {formatDate(plan.end_date)}
          </p>
        </div>
        <EnableSchedulePlan plan={plan} setPlanActivated={setPlanActivated} />
      </div>

      <div
        style={{
          marginTop: 10,
          width: "100%",
          borderRadius: 8,
          boxShadow: "0px 0px 8px rgba(0, 0, 0, 0.1)",
          border: "1px solid #ddd",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          background: "#fff",
          gap: 8,
          padding: 8,
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
          }}
        >
          <p style={{ margin: 0, color: "gray" }}>
            Số lượng chương trình cài đặt:&nbsp;&nbsp;
          </p>
          <p
            style={{
              margin: 0,
              fontSize: 16,
              fontWeight: "bold",
              color: "black",
            }}
          >
            {plan?.schedules?.length || 0}
          </p>
        </div>

        <Button
          type="default"
          style={{ borderRadius: 4 }}
          size="large"
          color="geekblue"
          icon={<RightOutlined />}
          onClick={() => setOpenDetailedSchedulePlanDrawer(true)}
        ></Button>
      </div>

      <Drawer
        title="Chi tiết kế hoạch"
        placement="right"
        onClose={() => setOpenDetailedSchedulePlanDrawer(false)}
        open={openDetailedSchedulePlanDrawer}
        width={"75%"}
      >
        <DetailedSchedulePlan
          plan={plan}
          onClose={() => setOpenDetailedSchedulePlanDrawer(false)}
        />
      </Drawer>
    </div>
  );
};

export default SchedulePlanContainer;
