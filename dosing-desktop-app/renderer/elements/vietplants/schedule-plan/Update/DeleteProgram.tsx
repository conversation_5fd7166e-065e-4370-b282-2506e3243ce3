import { DeleteOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Modal } from "antd";
import { FC, useState } from "react";
import useSchedulePlanStore from "../../../../stores/schedulePlanStore";
import { deleteScheduleProgram } from "../../../../services/schedule";
import { message } from "antd";

interface DeleteProgramProps {
  plan_id: string;
  program_id: string;
  enable_program: number;
}

const DeleteProgram: FC<DeleteProgramProps> = ({
  plan_id,
  program_id,
  enable_program,
}) => {
  const [open, setOpen] = useState(false);
  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();
  const handleDeleteProgram = async () => {
    const res = await deleteScheduleProgram(program_id);
    if (res?.statusOK) {
      const updatedPlans = schedulePlans.map((plan) => {
        if (plan.name === plan_id) {
          return {
            ...plan,
            schedules: plan.schedules.filter(
              (schedule) => schedule.id !== program_id
            ),
          };
        }
        return plan;
      });
      setSchedulePlans(updatedPlans);
      message.success("Xó<PERSON> chương trình thành công");
    }
    setOpen(false);
  };
  return (
    <div>
      <Button
        icon={<DeleteOutlined />}
        danger
        onClick={() => {
          if (enable_program == 1) {
            message.error(
              "Chương trình đang được kích hoạt! Vui lòng tắt trước"
            );
            return;
          }
          setOpen(true);
        }}
        style={{ borderRadius: 8 }}
      />
      <Modal
        open={open}
        onOk={handleDeleteProgram}
        onCancel={() => setOpen(false)}
        okButtonProps={{
          type: "primary",
          title: "Xóa",
          danger: true,
        }}
        okText="Xóa"
        cancelButtonProps={{
          type: "default",
          title: "Hủy",
        }}
        cancelText="Hủy"
      >
        <p
          style={{ textAlign: "center", fontWeight: "bold", fontSize: "14px" }}
        >
          Bạn có chắc chắn muốn xóa chương trình này?
        </p>
      </Modal>
    </div>
  );
};

export default DeleteProgram;
