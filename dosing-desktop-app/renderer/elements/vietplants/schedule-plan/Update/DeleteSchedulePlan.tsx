import { FC, useState } from "react";
import useSchedulePlanStore, {
  SchedulePlanProps,
} from "../../../../stores/schedulePlanStore";
import { Button, message, Modal } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { deleteSchedulePlan } from "../../../../services/schedule";

interface DeleteSchedulePlanProps {
  plan_id: string;
}

const DeleteSchedulePlan: FC<DeleteSchedulePlanProps> = ({ plan_id }) => {
  const [open, setOpen] = useState(false);

  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();
  const handleDeleteSchedulePlan = async () => {
    const res = await deleteSchedulePlan(plan_id);
    if (res?.statusOK) {
      const updatedPlans = schedulePlans.filter(
        (plan) => plan.name !== plan_id
      );
      setSchedulePlans(updatedPlans);
      message.success("Xóa kế hoạch thành công");
    }
    setOpen(false);
  };
  return (
    <div>
      <Button icon={<DeleteOutlined />} danger onClick={() => setOpen(true)}>
        Xóa kế hoạch
      </Button>
      <Modal
        open={open}
        onOk={handleDeleteSchedulePlan}
        onCancel={() => setOpen(false)}
        okButtonProps={{
          type: "primary",
          title: "Xóa",
          danger: true,
        }}
        okText="Xóa"
        cancelButtonProps={{
          type: "default",
          title: "Hủy",
        }}
        cancelText="Hủy"
      >
        <p
          style={{ textAlign: "center", fontWeight: "bold", fontSize: "14px" }}
        >
          Bạn có chắc chắn muốn xóa kế hoạch này?
        </p>
      </Modal>
    </div>
  );
};

export default DeleteSchedulePlan;
