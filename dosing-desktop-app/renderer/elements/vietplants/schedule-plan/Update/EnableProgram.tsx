import { message, Switch } from "antd";
import { FC } from "react";
import useSchedulePlanStore, {
  ScheduleProgram,
} from "../../../../stores/schedulePlanStore";
import { updateScheduleProgram } from "../../../../services/schedule";

interface EnableProgramProps {
  program: ScheduleProgram;
  plan_id: string;
  programActivated: number;
  setProgramActivated: (programActivated: number) => void;
}

const EnableProgram: FC<EnableProgramProps> = ({
  program,
  plan_id,
  programActivated,
  setProgramActivated,
}) => {
  console.log("program with id" + program.id + "is: " + programActivated);
  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();
  const handleChangeEnable = async (e: boolean) => {
    const program_to_update = {
      ...program,
      enable: e ? 1 : 0,
    };
    const res = await updateScheduleProgram(program_to_update);
    if (res?.statusOK) {
      const updatedPlans = schedulePlans.map((plan) => {
        if (plan.name === plan_id) {
          return {
            ...plan,
            schedules: plan.schedules.map((schedule) => {
              if (schedule.id === program_to_update.id) {
                return program_to_update;
              }
              return schedule;
            }),
          };
        }
        return plan;
      });
      setSchedulePlans(updatedPlans);
      message.success(
        `${e ? "Kích hoạt" : "Tắt"} chương trình thành công: ${
          program_to_update.name
        }`
      );
      console.log(program_to_update);
      setProgramActivated(e ? 1 : 0);
    }
  };

  return (
    <Switch
      style={{ backgroundColor: programActivated ? "#45c3a1" : "#ccc" }}
      checked={programActivated === 0 ? false : true}
      onChange={(e) => handleChangeEnable(e)}
    />
  );
};

export default EnableProgram;
