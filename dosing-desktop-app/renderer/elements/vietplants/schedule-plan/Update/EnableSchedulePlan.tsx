import { message, Switch } from "antd";
import { FC } from "react";
import { SchedulePlanProps } from "../../../../stores/schedulePlanStore";
import { updateSchedulePlan } from "../../../../services/schedule";
import useSchedulePlanStore from "../../../../stores/schedulePlanStore";

interface EnableSchedulePlanProps {
  plan: SchedulePlanProps;
  setPlanActivated: (activated: boolean) => void;
}

const EnableSchedulePlan: FC<EnableSchedulePlanProps> = ({
  plan,
  setPlanActivated,
}) => {
  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();
  const handleChangeEnable = async (e: boolean) => {
    const plan_to_update = {
      name: plan.name,
      label: plan.label,
      device_id: plan.device_id,
      start_date: plan.start_date,
      end_date: plan.end_date,
      enable: e ? 1 : 0,
    };
    const res = await updateSchedulePlan(plan_to_update);
    if (res?.statusOK) {
      const updatedPlans = schedulePlans.map((plan) => {
        if (plan.name === plan_to_update.name) {
          return {
            ...plan,
            enable: plan_to_update.enable,
          };
        }
        return plan;
      });
      setSchedulePlans(updatedPlans);
      message.success(
        `${e ? "Kích hoạt" : "Tắt"} kế hoạch thành công: ${
          plan_to_update.label
        }`
      );
      console.log(plan_to_update);
      setPlanActivated(e);
    }
  };
  return (
    <Switch
      checked={plan.enable !== 0 ? true : false}
      onChange={(e) => handleChangeEnable(e)}
      style={{ backgroundColor: plan.enable !== 0 ? "#45c3a1" : "#ccc" }}
    />
  );
};

export default EnableSchedulePlan;
