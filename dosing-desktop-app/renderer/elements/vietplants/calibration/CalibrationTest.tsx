import { <PERSON><PERSON>, <PERSON>, Alert, Typography, Input, Space } from "antd";
import { FC, useState } from "react";
import useControlDevice from "../../../services/device/useControlDevice";
import useDeviceDataStore from "../../../stores/deviceDataStore";
import { useMqttStore } from "../../../stores/mqttStore";
import { genDeviceTopic } from "../../../stores/mqttStore.utils";

const { Text, Paragraph } = Typography;

const CalibrationTest: FC = () => {
  const { deviceId } = useDeviceDataStore();
  const { run: control } = useControlDevice();
  const { subscribe, unsubscribe, connected } = useMqttStore();

  const [testPumpIndex, setTestPumpIndex] = useState(1);
  const [testActualMl, setTestActualMl] = useState(120);
  const [testSetMl, setTestSetMl] = useState(100);
  const [testOldCalib, setTestOldCalib] = useState(1.2);
  const [mqttMessages, setMqttMessages] = useState<string[]>([]);

  // Test function to send actual ML value
  const testSendActualMl = async () => {
    const actualMlKey = `CALIB_ACTUAL_ML_BOM_${testPumpIndex}`;

    try {
      await control({
        device_id_thingsboard: deviceId,
        method: "set_state",
        params: {
          [actualMlKey]: testActualMl,
        },
      });

      console.log(`✅ Sent ${actualMlKey} = ${testActualMl}`);
      alert(`Đã gửi ${actualMlKey} = ${testActualMl}`);
    } catch (error) {
      console.error("❌ Error sending actual ML:", error);
      alert("Lỗi khi gửi giá trị thực tế");
    }
  };

  // Test function to send SET ML value
  const testSendSetMl = async () => {
    const setMlKey = `HOLDING_SETML_BOM_${testPumpIndex}`;

    try {
      await control({
        device_id_thingsboard: deviceId,
        method: "set_state",
        params: {
          [setMlKey]: testSetMl,
        },
      });

      console.log(`✅ Sent ${setMlKey} = ${testSetMl}`);
      alert(`Đã gửi ${setMlKey} = ${testSetMl}`);
    } catch (error) {
      console.error("❌ Error sending SET ML:", error);
      alert("Lỗi khi gửi SET ML");
    }
  };

  // Test function to send old calibration value
  const testSendOldCalib = async () => {
    const oldCalibKey = `HOLDING_CALIB_BOM_${testPumpIndex}`;

    try {
      await control({
        device_id_thingsboard: deviceId,
        method: "set_state",
        params: {
          [oldCalibKey]: testOldCalib,
        },
      });

      console.log(`✅ Sent ${oldCalibKey} = ${testOldCalib}`);
      alert(`Đã gửi ${oldCalibKey} = ${testOldCalib}`);
    } catch (error) {
      console.error("❌ Error sending old calib:", error);
      alert("Lỗi khi gửi hệ số cũ");
    }
  };

  // Test function to trigger calibration
  const testTriggerCalibration = async () => {
    const calculateKey = `CALCULATE_CALIB_BOM_${testPumpIndex}`;

    try {
      await control({
        device_id_thingsboard: deviceId,
        method: "set_state",
        params: {
          [calculateKey]: true,
        },
      });

      console.log(`✅ Sent ${calculateKey} = true`);
      alert(`Đã gửi ${calculateKey} = true`);
    } catch (error) {
      console.error("❌ Error triggering calibration:", error);
      alert("Lỗi khi trigger hiệu chuẩn");
    }
  };

  // Test MQTT subscription
  const testMqttSubscription = () => {
    if (!deviceId) {
      alert("Không có deviceId");
      return;
    }

    const topic = genDeviceTopic(deviceId);
    console.log("Subscribing to topic:", topic);

    const subscriptionIds = subscribe([topic], (message) => {
      console.log("📨 MQTT Message received:", message);
      setMqttMessages((prev) => [
        `${new Date().toLocaleTimeString()}: ${message}`,
        ...prev.slice(0, 4), // Keep only last 5 messages
      ]);

      try {
        const data = JSON.parse(message);
        if (Array.isArray(data)) {
          const calculateFlags = data.filter(
            (item) => item.key && item.key.startsWith("CALCULATE_CALIB_BOM_")
          );
          if (calculateFlags.length > 0) {
            console.log("🔧 Calibration flags found:", calculateFlags);
          }
        }
      } catch (error) {
        console.error("Error parsing MQTT message:", error);
      }
    });

    console.log("Subscription IDs:", subscriptionIds);

    // Auto unsubscribe after 30 seconds
    setTimeout(() => {
      unsubscribe(subscriptionIds);
      console.log("Auto unsubscribed from test");
    }, 30000);
  };

  return (
    <Card title="🧪 Calibration Test Tool" style={{ marginBottom: 16 }}>
      <Alert
        message="Test Tool"
        description="Công cụ này giúp test các API calls và MQTT subscription cho calibration"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Space direction="vertical" style={{ width: "100%" }}>
        <div>
          <Text strong>Device ID: </Text>
          <Text code>{deviceId || "Chưa có"}</Text>
        </div>

        <div>
          <Text strong>MQTT Connected: </Text>
          <Text style={{ color: connected ? "green" : "red" }}>
            {connected ? "✅ Connected" : "❌ Disconnected"}
          </Text>
        </div>

        <div>
          <Text strong>Test Pump Index: </Text>
          <Input
            type="number"
            value={testPumpIndex}
            onChange={(e) => setTestPumpIndex(Number(e.target.value))}
            style={{ width: 100, marginLeft: 8 }}
            min={1}
            max={14}
          />
        </div>

        <div>
          <Text strong>Test SET ML: </Text>
          <Input
            type="number"
            value={testSetMl}
            onChange={(e) => setTestSetMl(Number(e.target.value))}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <div>
          <Text strong>Test Old Calib: </Text>
          <Input
            type="number"
            step="0.01"
            value={testOldCalib}
            onChange={(e) => setTestOldCalib(Number(e.target.value))}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <div>
          <Text strong>Test Actual ML: </Text>
          <Input
            type="number"
            value={testActualMl}
            onChange={(e) => setTestActualMl(Number(e.target.value))}
            style={{ width: 100, marginLeft: 8 }}
          />
        </div>

        <Space wrap>
          <Button type="primary" onClick={testSendSetMl}>
            Test Send SET ML
          </Button>
          <Button type="primary" onClick={testSendOldCalib}>
            Test Send Old Calib
          </Button>
          <Button type="primary" onClick={testSendActualMl}>
            Test Send Actual ML
          </Button>
          <Button type="primary" onClick={testTriggerCalibration}>
            Test Trigger Calibration
          </Button>
          <Button onClick={testMqttSubscription}>Test MQTT Subscription</Button>
        </Space>

        {mqttMessages.length > 0 && (
          <div>
            <Text strong>Recent MQTT Messages:</Text>
            <div
              style={{
                backgroundColor: "#f5f5f5",
                padding: 8,
                borderRadius: 4,
                maxHeight: 200,
                overflow: "auto",
              }}
            >
              {mqttMessages.map((msg, index) => (
                <div
                  key={index}
                  style={{ fontSize: 11, fontFamily: "monospace" }}
                >
                  {msg}
                </div>
              ))}
            </div>
          </div>
        )}

        <Alert
          message="Expected Keys"
          description={
            <div>
              <Paragraph style={{ margin: 0 }}>
                <Text code>HOLDING_SETML_BOM_{testPumpIndex}</Text> - Lưu lượng
                bơm
              </Paragraph>
              <Paragraph style={{ margin: 0 }}>
                <Text code>HOLDING_CALIB_BOM_{testPumpIndex}</Text> - Hệ số hiệu
                chuẩn cũ
              </Paragraph>
              <Paragraph style={{ margin: 0 }}>
                <Text code>CALIB_ACTUAL_ML_BOM_{testPumpIndex}</Text> - Giá trị
                thực tế đo được
              </Paragraph>
              <Paragraph style={{ margin: 0 }}>
                <Text code>CALCULATE_CALIB_BOM_{testPumpIndex}</Text> - Trigger
                tính toán
              </Paragraph>
            </div>
          }
          type="info"
        />
      </Space>
    </Card>
  );
};

export default CalibrationTest;
