import { Card, Switch, Space, Typography, Divider, Alert } from "antd";
import { FC, useState, useEffect } from "react";
import { DEBUG_CONFIG, toggleDebugMode, toggleDebugFeature, shouldShowDebug } from "../../../utils/debugMode";

const { Text, Title } = Typography;

const DebugControlPanel: FC = () => {
  const [debugEnabled, setDebugEnabled] = useState(DEBUG_CONFIG.isDebugEnabled);
  const [features, setFeatures] = useState(DEBUG_CONFIG.features);

  // Update local state when config changes
  useEffect(() => {
    const interval = setInterval(() => {
      setDebugEnabled(DEBUG_CONFIG.isDebugEnabled);
      setFeatures({ ...DEBUG_CONFIG.features });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleToggleDebug = () => {
    toggleDebugMode();
    setDebugEnabled(DEBUG_CONFIG.isDebugEnabled);
  };

  const handleToggleFeature = (feature: keyof typeof DEBUG_CONFIG.features) => {
    toggleDebugFeature(feature);
    setFeatures({ ...DEBUG_CONFIG.features });
  };

  if (!shouldShowDebug()) {
    return null;
  }

  return (
    <Card
      title="🔧 Debug Control Panel"
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Alert
        message="Debug Mode"
        description="Các tính năng debug chỉ hiển thị khi được bật. Sử dụng để troubleshoot và test."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Space direction="vertical" style={{ width: "100%" }}>
        <div>
          <Title level={5}>Master Debug Control</Title>
          <Space>
            <Switch
              checked={debugEnabled}
              onChange={handleToggleDebug}
              checkedChildren="ON"
              unCheckedChildren="OFF"
            />
            <Text>
              Debug Mode: <Text strong>{debugEnabled ? "Enabled" : "Disabled"}</Text>
            </Text>
          </Space>
          <Text type="secondary" style={{ display: "block", fontSize: 11 }}>
            Environment: {DEBUG_CONFIG.isDevelopment ? "Development" : "Production"}
          </Text>
        </div>

        <Divider />

        <div>
          <Title level={5}>Debug Features</Title>
          <Space direction="vertical">
            <Space>
              <Switch
                checked={features.calibrationDebugInfo}
                onChange={() => handleToggleFeature('calibrationDebugInfo')}
                disabled={!debugEnabled}
                size="small"
              />
              <Text>Calibration Debug Info</Text>
            </Space>

            <Space>
              <Switch
                checked={features.calibrationTestTool}
                onChange={() => handleToggleFeature('calibrationTestTool')}
                disabled={!debugEnabled}
                size="small"
              />
              <Text>Calibration Test Tool</Text>
            </Space>

            <Space>
              <Switch
                checked={features.apiLogger}
                onChange={() => handleToggleFeature('apiLogger')}
                disabled={!debugEnabled}
                size="small"
              />
              <Text>API Logger</Text>
            </Space>

            <Space>
              <Switch
                checked={features.consoleLogging}
                onChange={() => handleToggleFeature('consoleLogging')}
                disabled={!debugEnabled}
                size="small"
              />
              <Text>Console Logging</Text>
            </Space>
          </Space>
        </div>

        <Divider />

        <Alert
          message="Console Commands"
          description={
            <div>
              <Text code style={{ fontSize: 10 }}>
                window.calibrationDebug.toggle() - Toggle debug mode
              </Text>
              <br />
              <Text code style={{ fontSize: 10 }}>
                window.calibrationDebug.toggleFeature('calibrationDebugInfo') - Toggle specific feature
              </Text>
              <br />
              <Text code style={{ fontSize: 10 }}>
                window.calibrationDebug.config - View current config
              </Text>
            </div>
          }
          type="info"
          style={{ fontSize: 11 }}
        />
      </Space>
    </Card>
  );
};

export default DebugControlPanel;
