import { <PERSON><PERSON>, Pop<PERSON> } from "antd";
import { FunctionList } from "../../../services/device/devices";
import { LogoutOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";

const ModalPreventAction = ({
  functionItem,
  handleCancelCalibration,
  setIsCalibProgressFinished,
}: {
  functionItem: FunctionList;
  handleCancelCalibration: () => void;
  setIsCalibProgressFinished: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [openPopover, setOpenPopover] = useState(false);

  useEffect(() => {
    setIsCalibProgressFinished(false);
  }, []);

  return (
    <div
      style={{
        zIndex: 999,
        height: "100vh",
        width: "100vw",
        position: "fixed",
        top: 0,
        left: 0,
        backgroundColor: "rgba(0,0,0,0.1)",
        backdropFilter: "blur(1px)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "20px",
        boxSizing: "border-box",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 12,
          background: "#fff",
          borderRadius: 16,
          padding: 20,
          minWidth: 300,
          maxWidth: "90vw",
          width: "fit-content",
          minHeight: 180,
          maxHeight: "90vh",
          justifyContent: "center",
          alignItems: "center",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
          overflow: "auto",
          boxSizing: "border-box",
        }}
      >
        <p
          style={{
            fontSize: 16,
            textAlign: "center",
            margin: "0 0 16px 0",
            wordWrap: "break-word",
            lineHeight: 1.4,
          }}
        >
          Đang hiệu chuẩn cho Bơm{" "}
          {functionItem.identifier
            ? functionItem.identifier.match(/\d+$/)?.[0] ||
              functionItem.identifier
            : ""}
        </p>

        <div
          style={{
            display: "flex",
            gap: 12,
            flexDirection: "column",
            width: "100%",
            maxWidth: 280,
          }}
        >
          <Button
            type="primary"
            onClick={() => {
              handleCancelCalibration();
              setIsCalibProgressFinished(true);
            }}
            style={{
              backgroundColor: "#52c41a",
              borderColor: "#52c41a",
              width: "100%",
              height: "auto",
              padding: "8px 16px",
              whiteSpace: "normal",
              wordWrap: "break-word",
            }}
          >
            ✅ Hoàn thành và nhập kết quả
          </Button>

          <Button
            danger
            type="primary"
            onClick={() => setOpenPopover(true)}
            icon={<LogoutOutlined />}
            style={{
              width: "100%",
              height: "auto",
              padding: "8px 16px",
              whiteSpace: "normal",
              wordWrap: "break-word",
            }}
          >
            ❌ Hủy bỏ hiệu chuẩn
          </Button>
        </div>
        <Popover
          title="Hủy bỏ hiệu chuẩn"
          content={
            <>
              <p>Điều này sẽ hủy bỏ quá trình hiệu chuẩn của bơm</p>
              <p>Bạn có chắc chắn muốn làm điều đó không?</p>
              <Button
                danger
                type="primary"
                onClick={() => handleCancelCalibration()}
                icon={<LogoutOutlined />}
              >
                OK
              </Button>
            </>
          }
          trigger="click"
          open={openPopover}
          onOpenChange={(e) => setOpenPopover(e)}
        ></Popover>
      </div>
    </div>
  );
};

export default ModalPreventAction;
