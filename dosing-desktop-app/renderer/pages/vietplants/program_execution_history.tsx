import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "antd";
import { useEffect, useState } from "react";
import { getProgramExecutionHistory } from "../../services/program-execution-history";
import useProgramExecutionHistoryStore from "../../stores/programExecutionHistoryStore";
import dayjs from "dayjs";
import PEHContainer from "../../elements/vietplants/program-execution-history/PEHContainer";
import { LoadingOutlined } from "@ant-design/icons";

export default function ProgramExecutionHistoryPage() {
  const {
    startTime,
    endTime,
    setStartTime,
    setEndTime,
    programExecutionHistory,
    setProgramExecutionHistory,
  } = useProgramExecutionHistoryStore();

  const [listOfHistoryIsLoading, setListOfHistoryIsLoading] = useState<
    "unfetch" | "fetching" | "fetched"
  >("unfetch");

  async function fetchList() {
    setListOfHistoryIsLoading("fetching");
    try {
      const response = await getProgramExecutionHistory({
        filters: [
          ["iot_schedule_log", "start_time", ">=", startTime],
          ["iot_schedule_log", "end_time", "<=", endTime],
        ],
      });
      setProgramExecutionHistory(response?.result.data);
    } catch (error) {
      console.log(error);
    }
    setListOfHistoryIsLoading("fetched");
  }

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        padding: 16,
      }}
    >
      <p style={{ fontSize: 24, fontWeight: "bold" }}>Lịch sử chương trình</p>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={10}>
          <DatePicker
            showTime
            style={{ width: "100%" }}
            placeholder="Chọn thời điểm bắt đầu"
            defaultValue={
              startTime ? dayjs(startTime) : dayjs().subtract(7, "days")
            }
            onChange={(date) => {
              setStartTime(date?.format("YYYY-MM-DD HH:mm:ss") || "");
            }}
          />
        </Col>
        <Col span={10}>
          <DatePicker
            showTime
            style={{ width: "100%" }}
            placeholder="Chọn thời điểm kết thúc"
            defaultValue={endTime ? dayjs(endTime) : dayjs()}
            onChange={(date) => {
              setEndTime(date?.format("YYYY-MM-DD HH:mm:ss") || "");
            }}
          />
        </Col>
        <Col span={4}>
          <Button
            onClick={() => {
              fetchList();
            }}
          >
            OK
          </Button>
        </Col>
      </Row>

      <div style={{ height: 400, width: "100%", overflowY: "scroll" }}>
        {listOfHistoryIsLoading === "unfetch" &&
          programExecutionHistory.length > 0 &&
          programExecutionHistory?.map((item, index) => {
            return <PEHContainer key={index} stt={index + 1} pehData={item} />;
          })}
        {listOfHistoryIsLoading === "fetching" && (
          <p
            style={{
              textAlign: "center",
              marginTop: 50,
              color: "gray",
              fontSize: 16,
            }}
          >
            <LoadingOutlined style={{ marginRight: 16 }} />
            Đang tải lịch sử chương trình...
          </p>
        )}
        {listOfHistoryIsLoading === "fetched" &&
          (programExecutionHistory.length === 0 ? (
            <p
              style={{
                textAlign: "center",
                marginTop: 50,
                color: "gray",
                fontSize: 16,
              }}
            >
              Không có lịch sử chương trình !
            </p>
          ) : (
            programExecutionHistory?.map((item, index) => {
              return (
                <PEHContainer key={index} stt={index + 1} pehData={item} />
              );
            })
          ))}
      </div>
    </div>
  );
}
