import { Card, Col, Row } from "antd";
import ChartRender from "./ChartRender";
import { FunctionList } from "../../services/device/devices";
import { FC } from "react";

interface MonitorDeviceProps {
  functionItem: FunctionList;
  lineColor?: string;
}

const MonitorDevice: FC<MonitorDeviceProps> = ({ functionItem, lineColor }) => {
  return (
    <div
      style={{
        boxShadow: "0px 1px 10px -5px rgba(0, 0, 0, 0.1)",
        backgroundColor: "#fff",
        borderRadius: 8,
      }}
    >
      {functionItem.show_chart ? (
        <ChartRender
          dataFunction={functionItem}
          deviceId={functionItem.device_id_thingsboard}
          deviceKey={functionItem.identifier as string}
          lineColor={lineColor}
        />
      ) : (
        <></>
      )}
    </div>
  );
};

export default MonitorDevice;
