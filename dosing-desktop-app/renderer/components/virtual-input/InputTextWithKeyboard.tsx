import React, { useState, useRef, useEffect } from "react";
import { Input } from "antd";
import Keyboard from "react-simple-keyboard";
import "react-simple-keyboard/build/css/index.css";

const InputTextWithKeyboard = React.forwardRef((props: any, ref: any) => {
  const [inputValue, setInputValue] = useState("");
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const keyboardInstanceRef = useRef(null);
  const inputRef = useRef(null);
  const keyboardWrapperRef = useRef(null);
  const [layoutName, setLayoutName] = useState("default");

  useEffect(() => {
    if (ref) {
      ref.current = inputRef.current;
    }
  }, [ref]);

  useEffect(() => {
    if (
      props.value !== undefined &&
      props.value !== null &&
      String(props.value) !== inputValue
    ) {
      setInputValue(String(props.value));
    }
  }, [props.value]);

  const handleKeyboardChange = (input) => {
    setInputValue(input);
    if (props.onChange) {
      props.onChange({ target: { value: input, name: props.name } });
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    if (props.onChange) {
      props.onChange(e);
    }
  };

  const handleFocus = () => {
    setKeyboardVisible(true);
    setTimeout(() => {
      keyboardInstanceRef.current?.setInput(inputValue);
    }, 100);
  };

  const handleBlur = () => {
    setTimeout(() => {
      const activeElement = document.activeElement;
      const isInsideKeyboard = activeElement?.closest(".rsk-container");
      const isInputStillFocused = activeElement === inputRef.current;

      if (!isInsideKeyboard && !isInputStillFocused) {
        setKeyboardVisible(false);
      }
    }, 200);
  };

  const handleKeyPress = (button) => {
    if (button === "{shift}" || button === "{lock}") {
      const newLayoutName = layoutName === "default" ? "shift" : "default";
      setLayoutName(newLayoutName);
      return;
    }

    if (button === "{enter}") {
      setKeyboardVisible(false);
      inputRef.current?.blur();
      return;
    }

    if (button === "{bksp}") {
      const updatedValue = inputValue.slice(0, -1);
      handleKeyboardChange(updatedValue);
      return;
    }

    if (button === "{space}") {
      handleKeyboardChange(inputValue + " ");
      return;
    }
    if (button === "{enter}") {
      setKeyboardVisible(false);
      if (inputRef.current) {
        inputRef.current.blur();
      }
    }

    handleKeyboardChange(inputValue + button);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        inputRef.current &&
        !inputRef.current.input.contains(event.target) &&
        !keyboardWrapperRef.current?.contains(event.target)
      ) {
        setKeyboardVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <Input
        {...props}
        ref={inputRef}
        value={inputValue}
        onChange={handleInputChange}
        onFocus={handleFocus}
        placeholder={props.placeholder ?? ""}
        size={props.size ?? "middle"}
        type={props.type}
        // onBlur={handleBlur}
      />
      {keyboardVisible && (
        <div
          ref={keyboardWrapperRef}
          className="rsk-container"
          style={{
            position: "fixed",
            bottom: 0,
            left: 0,
            width: "100%",
            background: "#fff",
            boxShadow: "0 -2px 8px rgba(0,0,0,0.15)",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              padding: "8px 16px",
              borderBottom: "1px solid #e8e8e8",
              fontSize: "16px",
              fontWeight: "bold",
              background: "#fafafa",
              whiteSpace: "nowrap",
              overflowX: "auto",
            }}
          >
            {props.type === "password"
              ? "*".repeat(inputValue.length)
              : inputValue || "\u00A0"}
          </div>
          <Keyboard
            ref={keyboardInstanceRef}
            onChange={handleKeyboardChange}
            onKeyPress={handleKeyPress}
            layoutName={layoutName}
            layout={{
              default: [
                "1 2 3 4 5 6 7 8 9 0 - = {bksp}",
                "q w e r t y u i o p [ ] \\",
                "{lock} a s d f g h j k l ; ' {enter}",
                "{shift} z x c v b n m , . / {shift}",
                "{space}",
              ],
              shift: [
                "! @ # $ % ^ & * ( ) _ + {bksp}",
                "Q W E R T Y U I O P { } |",
                '{lock} A S D F G H J K L : " {enter}',
                "{shift} Z X C V B N M < > ? {shift}",
                "{space}",
              ],
            }}
            {...props.keyboardOptions}
          />
        </div>
      )}
    </>
  );
});

export default InputTextWithKeyboard;
