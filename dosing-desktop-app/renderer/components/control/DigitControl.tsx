import { But<PERSON>, Form, InputNumber } from "antd";
import { CSSProperties, FC, useEffect, useRef, useState } from "react";
import { FunctionList } from "../../services/device/devices";
// import InputNumberWithKeyboard from "../virtual-input/InputNumberWithKeyboard";
import useDeviceDataStore from "../../stores/deviceDataStore";
import useLatestDataDevice from "../../services/device/useLatestDataDevice";
import useControlDevice from "../../services/device/useControlDevice";
import { mqttStoreSelector, useMqttStore } from "../../stores/mqttStore";
import { MqttNoticeDataSub } from "../../events/mqtt/mqtt-device-eventemitter";
import { genDeviceTopic } from "../../stores/mqttStore.utils";
import InputNumberWithKeyboard from "../virtual-input/InputNumberWithKeyboard";

const styles: { [key: string]: CSSProperties } = {
  controlItem: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
    border: "1px solid rgb(230,230,230)",
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
};

interface DigitControlProps {
  functionItem: FunctionList;
  setLatestDigitValue?: React.Dispatch<
    React.SetStateAction<number | undefined>
  >;
}

const DigitControl: FC<DigitControlProps> = ({
  functionItem,
  setLatestDigitValue,
}) => {
  const { deviceData, deviceId } = useDeviceDataStore();
  const { data, loading: loadingData, run } = useLatestDataDevice();

  // check if latest data is set, it will allow to subscribe to mqtt event
  const [latestDataIsSet, setLatestDataIsSet] = useState(false);

  const { run: control, loading } = useControlDevice();
  const { handleMessage, client } = useMqttStore();

  const [value, setValue] = useState(0);

  function getData() {
    if (deviceData.name && functionItem.identifier) {
      console.log("Start to get new data");
      run({
        deviceId: deviceData?.name,
        keys: [functionItem.identifier!],
      });
    }
  }

  useEffect(() => {
    getData();
  }, [deviceData?.name, functionItem.identifier]);

  useEffect(() => {
    if (!deviceData?.name || !functionItem.identifier) return;
    const latestDataWithFunctionKey =
      data?.data?.[functionItem?.identifier] || [];
    console.log("latestDataWithFunctionKey", latestDataWithFunctionKey);
    if (latestDataWithFunctionKey.length > 0) {
      const latestData =
        latestDataWithFunctionKey[latestDataWithFunctionKey.length - 1];
      if (latestData) {
        setValue(Number(latestData?.value));
        if (setLatestDigitValue) {
          setLatestDigitValue(Number(latestData.value));
        }
      }
    }

    if (data?.data) {
      setLatestDataIsSet(true);
    }
  }, [data, deviceData?.name, functionItem.identifier]);

  useEffect(() => {
    if (!latestDataIsSet) return;
    const messageHandler = (topic, msg) => {
      if (topic !== genDeviceTopic(deviceId)) return;
      console.log(
        `DIGIT CONTROL: Received MQTT message on topic ${topic}:`,
        msg.toString()
      );
      try {
        const data: Array<{ ts: string; key: string; value: string | number }> =
          JSON.parse(msg.toString());
        if (Array.isArray(data)) {
          const deviceData = data.filter(
            (d) => d.key === functionItem.identifier
          );
          if (deviceData.length > 0) {
            const latestData = deviceData[deviceData.length - 1];
            setValue(Number(latestData?.value));
            if (setLatestDigitValue) {
              setLatestDigitValue(Number(latestData.value));
            }
          }
        }
      } catch (error) {
        console.error("MQTT message error:", error);
      }
    };

    client.on("message", messageHandler);

    return () => {
      client.off("message", messageHandler);
    };
  }, [handleMessage, deviceData?.name, functionItem.identifier]);

  const handlePushValue = async (value: number) => {
    const device_id_thingsboard = deviceData?.name;
    if (!device_id_thingsboard || !functionItem.identifier) return;

    await control({
      device_id_thingsboard: device_id_thingsboard,
      method: "set_state",
      params: {
        [functionItem.identifier]: value,
      },
    }).then(() => {
      setValue(0);
    });
    getData(); // Gọi lại hàm getData để lấy dữ liệu mới sau khi onChange thực thi thành công
  };

  return (
    <div style={styles.controlItem}>
      {/* <p style={{ margin: 0 }}>{functionItem.label}</p> */}
      <Form layout="vertical" style={{ width: "100%" }}>
        <div
          style={{
            width: "100%",
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            flexWrap: "wrap",
            gap: "8px",
            alignItems: "end",
          }}
        >
          <Form.Item
            style={{ width: "calc(100% - 70px)", marginBottom: 0 }}
            name={functionItem.name}
            label={
              <p style={{ color: "rgb(100,100,100)", margin: 0 }}>
                {functionItem.label}
              </p>
            }
            required
          >
            <InputNumberWithKeyboard
              style={{ width: "100%" }}
              onChange={(e) => setValue(e as number)}
              // placeholder={value?.toString()}
              defaultValue={value}
            />
            {/* <InputNumber
              style={{ width: "100%" }}
              placeholder={value?.toString()}
              onChange={(e) => setValue(e as number)}
              // min={0}
              // max={999}
              // precision={0}
              // step={1}
            /> */}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              style={{ width: "60px" }}
              onClick={() => handlePushValue(value)}
            >
              OK
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default DigitControl;
