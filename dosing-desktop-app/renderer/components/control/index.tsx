import { FunctionList } from "../../services/device/devices";
import OnOffControl from "./OnOffControl";
import DigitControl from "./DigitControl";

const DeviceControl = ({
  functionItem,
}: {
  functionItem: FunctionList;
  // deviceData: I_IOTDevice;
}) => {
  // const { run: control, loading } = useControlDevice();
  if (functionItem.data_type === "Bool") {
    return <OnOffControl functionItem={functionItem} />;
  } else if (functionItem.data_type === "Value") {
    return <DigitControl functionItem={functionItem} />;
  }
};

export default DeviceControl;
