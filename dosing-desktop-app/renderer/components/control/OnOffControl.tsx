import { CSSProperties, FC, useEffect, useRef, useState } from "react";
import { FunctionList } from "../../services/device/devices";
import useLanguageStore from "../../stores/languageStore";
import { generateAPIPath } from "../../services/utilities";
import { DashboardOutlined } from "@ant-design/icons";
import { Switch } from "antd";
import useLatestDataDevice from "../../services/device/useLatestDataDevice";
import useDeviceDataStore from "../../stores/deviceDataStore";
import useControlDevice from "../../services/device/useControlDevice";
import { mqttStoreSelector, useMqttStore } from "../../stores/mqttStore";
import { MqttNoticeDataSub } from "../../events/mqtt/mqtt-device-eventemitter";
import { genDeviceTopic } from "../../stores/mqttStore.utils";

const styles: { [key: string]: CSSProperties } = {
  controlItem: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
    border: "1px solid rgb(230,230,230)",
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
};

interface OnOffControlProps {
  functionItem: FunctionList;
  readonly?: boolean;
  setResponseStatusOfOnOffControl?: (value: boolean) => void;
}

const OnOffControl: FC<OnOffControlProps> = ({
  functionItem,
  readonly,
  setResponseStatusOfOnOffControl,
}) => {
  const { languageData } = useLanguageStore();

  const { deviceData, deviceId } = useDeviceDataStore();
  const { data, loading: loadingData, run } = useLatestDataDevice();

  // check if latest data is set, it will allow to subscribe to mqtt event
  const [latestDataIsSet, setLatestDataIsSet] = useState(false);

  const { run: control, loading } = useControlDevice();
  const { handleMessage, client } = useMqttStore();

  const [isOn, setIsOn] = useState(false);
  // const getIsOnline = (value: any) => toString(value) === "true";
  const getIsOnline = (value: any) => String(value) === "true";

  function getData() {
    if (deviceData.name && functionItem.identifier) {
      console.log("Start to get new data");
      run({
        deviceId: deviceData?.name,
        keys: [functionItem.identifier],
      });
    }
  }

  useEffect(() => {
    getData();
  }, [deviceData?.name, functionItem.identifier]);

  useEffect(() => {
    if (!deviceData?.name || !functionItem.identifier) return;

    const latestDataWithFunctionKey =
      data?.data?.[functionItem?.identifier] || [];
    console.log("latestDataWithFunctionKey", latestDataWithFunctionKey);

    if (latestDataWithFunctionKey.length > 0) {
      const latestData =
        latestDataWithFunctionKey[latestDataWithFunctionKey.length - 1];
      if (latestData) {
        setIsOn(getIsOnline(latestData?.value));
      }
    }

    if (data?.data) {
      setLatestDataIsSet(true);
    }
  }, [data, deviceData?.name, functionItem.identifier]);

  useEffect(() => {
    if (!latestDataIsSet) return;
    const messageHandler = (topic, msg) => {
      if (topic !== genDeviceTopic(deviceId)) return;
      console.log(
        `ON OFF CONTROL: Received MQTT message on topic ${topic}:`,
        msg.toString()
      );
      try {
        const data: Array<{ ts: string; key: string; value: string | number }> =
          JSON.parse(msg.toString());
        if (Array.isArray(data)) {
          const deviceData = data.filter(
            (d) => d.key === functionItem.identifier
          );
          if (deviceData.length > 0) {
            const latestData = deviceData[deviceData.length - 1];
            if (latestData) {
              setIsOn(getIsOnline(latestData?.value));
            }
          }
        }
      } catch (error) {
        console.error("MQTT message error:", error);
      }
    };

    client.on("message", messageHandler);

    return () => {
      client.off("message", messageHandler);
    };
  }, [
    latestDataIsSet,
    handleMessage,
    deviceData?.name,
    functionItem.identifier,
  ]);

  const onValueChange = async (value: boolean) => {
    const device_id_thingsboard = deviceData?.name;
    if (!device_id_thingsboard || !functionItem.identifier) return;

    await control({
      device_id_thingsboard: device_id_thingsboard,
      method: "set_state",
      params: {
        [functionItem.identifier]: value,
      },
    });
    getData(); // Gọi lại hàm getData để lấy dữ liệu mới sau khi onChange thực thi thành công
  };

  useEffect(() => {
    if (setResponseStatusOfOnOffControl) {
      setResponseStatusOfOnOffControl(isOn); // Cập nhật trạng thái của switch
    }
  }, [isOn]);

  return (
    <div style={styles.controlItem}>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
        }}
      >
        {functionItem.icon_url ? (
          <img
            height={"24px"}
            src={generateAPIPath(
              "api/v2/file/download?file_url=" + functionItem.icon_url
            )}
            onError={() => <DashboardOutlined />}
          />
        ) : (
          <DashboardOutlined />
        )}
        <p style={{ margin: 0, color: "rgb(100,100,100)" }}>
          {functionItem.label}
        </p>
      </div>
      <Switch
        disabled={readonly}
        value={isOn}
        onChange={onValueChange}
        checked={isOn}
        loading={loading || loadingData}
        checkedChildren={
          functionItem.data_on_text
            ? functionItem.data_on_text
            : languageData["common.control.switch.on"]
        }
        unCheckedChildren={
          functionItem.data_off_text
            ? functionItem.data_off_text
            : languageData["common.control.switch.off"]
        }
        style={{ minWidth: "60px" }}
      />
    </div>
  );
};

export default OnOffControl;
