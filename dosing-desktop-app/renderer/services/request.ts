import { message } from "antd";
import { useCallback, useState } from "react";

export async function request(
  url: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    data?: any;
    params?: Record<string, any>;
    [key: string]: any;
  }
) {
  const encodedHeaders = options.headers
    ? Object.fromEntries(
        Object.entries(options.headers).map(([key, value]) => [
          key,
          encodeURIComponent(value),
        ])
      )
    : {};

  let urlWithParams = url;
  if (options.params && Object.keys(options.params).length) {
    const params = new URLSearchParams();

    for (const [key, value] of Object.entries(options.params)) {
      if (key === "filters" && Array.isArray(value)) {
        params.set(key, JSON.stringify(value));
      } else {
        params.set(key, String(value));
      }
    }

    urlWithParams = `${url}?${params.toString()}`;
  }

  const { data, ...fetchOptions } = options;

  const includeAuth = options?.includeAuth ?? true;

  const response = await fetch(urlWithParams, {
    ...fetchOptions,
    headers: {
      ...encodedHeaders,
      ...(includeAuth && {
        Authorization: `Bearer ${encodeURIComponent(
          JSON.parse(localStorage.getItem("token") || "")?.token || ""
        )}`,
      }),
      "Content-Type": "application/json",
    },
    body: data ? JSON.stringify(data) : undefined,
  });

  const contentType = response.headers.get("Content-Type") || "";
  const isJson = contentType.includes("application/json");
  const responseData = isJson ? await response.json() : await response.text();

  // if (!response.ok) {
  //   message.error("Gửi yêu cầu thất bại");
  // }

  return {
    statusOK: response.ok,
    responseData: responseData,
  };
}

// import { ResponseError, extend } from "umi-request";
// import _ from "lodash";

// const errorHandler = function (error: ResponseError) {
//   if (error.response) {
//     throw {
//       success: false,
//       message: error.message?.toString(),
//       data:
//         _.get(error, "data.message", "") ||
//         _.get(error, "response.statusText", ""),
//       httpError: {
//         httpCode: error.response.status,
//         message: error.response.statusText,
//         rawData: error.request,
//       },
//     } as APIShema.ResponseStructure;
//   } else {
//     // The request was made but no response was received or error occurs when setting up the request.
//     throw {
//       data: null,
//       success: false,
//       message: error.message,
//     } as APIShema.ResponseStructure;
//   }
// };

// const request = extend({
//   errorHandler,
//   credentials: "same-origin", // default
// });

// // request interceptor, change url or options.
// request.interceptors.request.use((url, options) => {
//   options.headers = {
//     Authorization: `Bearer ${localStorage.getItem("token")}`,
//     ...options.headers,
//   };

//   return {
//     url: `${url}`,
//     options: {
//       ...options,
//       interceptors: true,
//     },
//   };
// });

// // clone response in response interceptor
// request.interceptors.response.use(async (response) => {
//   try {
//     const resHttpCode = response.status;
//     console.log(response?.token);
//     if (resHttpCode == 200) {
//       const result = await response?.clone()?.json();
//       const { status = "", message = "", data = {} } = result;

//       if (status !== "success" && status !== "") {
//         throw message;
//       } else {
//         return {
//           success: true,
//           data: data,
//           message: message,
//         } as any | APIShema.ResponseStructure;
//       }
//     } else {
//       const result = await response?.clone()?.json();
//       const { message = "" } = result;
//       if (message) throw message;
//       else throw `Error code ${response.status}: ${response.statusText}`;
//     }
//   } catch (error: any) {
//     throw {
//       success: false,
//       message: error.toString(),
//       data: null,
//       response: response,
//     };
//   }
// });

// export default request;

interface UseRequestOptions<T> {
  manual?: boolean;
  onSuccess?: (data: any, params: T) => void;
  onError?: (e: any, params: T) => void;
}

export function useRequest<T extends any[] = any[]>(
  service: (...args: T) => Promise<any>,
  options?: UseRequestOptions<T>
) {
  const { onSuccess, onError } = options || {};
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<any>(null);

  const run = useCallback(
    async (...params: T) => {
      setLoading(true);
      setError(null);
      try {
        const result = await service(...params);
        setData(result);
        onSuccess?.(result, params);
        return result;
      } catch (e) {
        setError(e);
        onError?.(e, params);
        return Promise.reject(e);
      } finally {
        setLoading(false);
      }
    },
    [service, onSuccess, onError]
  );

  return {
    loading,
    data,
    error,
    run,
  };
}
