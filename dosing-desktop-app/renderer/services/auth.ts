import { request } from "./request";
import { generateAPIPath } from "./utilities";

export async function getListAllValidUsers() {
  return request(generateAPIPath("api/v2/customerUser/user"), {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function login(
  body: { usr: string; pwd: string },
  options?: Record<string, any>
) {
  return request(generateAPIPath("api/v2/auth/login"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    includeAuth: false,
    ...(options || {}),
  });
}

export const loginOut = async () => {
  return request(generateAPIPath("api/auth/logout"), {
    method: "GET",
    includeAuth: false,
  });
};

export async function forgotPassword(
  body: { email: string },
  options?: Record<string, any>
) {
  return request(generateAPIPath("api/v2/auth/reset-by-email-uuid"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

export async function resetPasswordByToken(
  body: { key: string; new_password: string },
  options?: Record<string, any>
) {
  return request(generateAPIPath("api/v2/auth/reset-password-uuid"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

export async function userResetPassword(
  body: { new_password: string },
  options?: Record<string, any>
) {
  return request(generateAPIPath("api/auth/user-change-password"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
