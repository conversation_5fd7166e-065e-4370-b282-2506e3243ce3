import { getLatestDataDevices } from "./devices";
import { useRequest } from "../request";

const useLatestDataDevice = (
  { onSuccess } = {} as {
    onSuccess: (data: any) => void;
  }
) => {
  const req = useRequest(getLatestDataDevices, {
    onError(e, params) {
      // message.error('Không thành công');
    },
    onSuccess(data, params) {
      // message.success('Thành công');
      onSuccess?.(data);
    },
  });
  return req;
};

export default useLatestDataDevice;
