import { controlDevice } from "./devices";
import { useRequest } from "../request";
import { message } from "antd";

const useControlDevice = (
  { onSuccess } = {} as {
    onSuccess: (data: any) => void;
  }
) => {
  const req = useRequest(controlDevice, {
    onError(e, params) {
      // message.error("Điều khiển thất bại");
    },
    onSuccess(data, params) {
      // message.success("Điều khiển thành công");
      onSuccess?.(data);
    },
    manual: true,
  });
  return req;
};

export default useControlDevice;
