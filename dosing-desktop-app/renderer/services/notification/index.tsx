import { jwtDecode } from "jwt-decode";
import { getAccessToken } from "../../utils/localStorage";
import { request } from "../request";
import { generateAPIPath } from "../utilities";
import { ITokenInfo } from "../../types/auth.type";

export class IGeneralDoc {
  filters?: string;
  or_filters?: string;
  page?: number;
  size?: number;
  fields?: string;
  order_by?: string;
  group_by?: string;
  permission?: string;
}

export enum RoleEnum {
  TECHNICIAN_EMPLOYEE = "TECHNICIAN_EMPLOYEE",
  CUSTOMER_ADMIN = "CUSTOMER_ADMIN",
  ADMIN_WAREHOUSE = "ADMIN_WAREHOUSE",
}

export const getInfoFromAccessToken = () => {
  try {
    const token = getAccessToken();
    const info = jwtDecode(token as string) as ITokenInfo;
    if (info.is_admin) {
      info.user_role.push(RoleEnum.CUSTOMER_ADMIN);
    }
    // return {
    //   ...info,
    //   // user_role:Object.keys(RoleEnum)
    //   // user_role: [RoleEnum.TECHNICIAN_EMPLOYEE],
    //   user_role:[...info.user_role,]
    // } as ITokenInfo;
    return info;
  } catch (error) {
    return null;
  }
};
export const getUserIdFromToken = () => {
  const info = getInfoFromAccessToken();
  return info?.user_id;
};
export const getCustomerIdFromToken = () => {
  const info = getInfoFromAccessToken();
  return info?.customer_id;
};

export type NoticeIconItemType =
  | "task"
  | "other"
  | "approval request"
  | "approval response"
  | "device";

export type NoticeDataRes = {
  name?: string;
  creation?: string;
  modified?: string;
  modified_by?: any;
  owner?: string;
  docstatus?: number;
  idx?: number;
  customer_user?: string;
  message?: string;
  created_at?: string;
  entity?: string;
  type?: NoticeIconItemType;
  is_read?: number | boolean;
  _user_tags?: any;
  _comments?: any;
  _assign?: any;
  _liked_by?: any;
  is_sent?: number;
  customer_id?: string;
  err_code?: any;
  entity_label?: string;
  severity?: string;
};

type PaginationResponseResult<T> = {
  result: {
    data: T;
    pagination: {
      pageNumber: number;
      pageSize: number;
      totalElements: number;
      totalPages: number;
      order_by: string;
    };
  };
};

export const getWebNotificationList = async (params?: any) => {
  const res = await request(generateAPIPath(`api/v2/web-notification`), {
    method: "GET",
    params: {
      ...params,
      order_by: "created_at desc",
      or_filters: JSON.stringify([
        ["iot_notification", "customer_id", "=", getCustomerIdFromToken()],
        ["iot_notification", "customer_user", "=", getUserIdFromToken()],
      ]),
      filters: JSON.stringify([
        ...(params?.filters || []),
        // ...[['iot_notification', 'is_read', '=', 0]],
      ]),
    },
  });
  return (res.responseData as PaginationResponseResult<NoticeDataRes[]>)?.result
    ?.data;
};
export const getWebNotificationListAll = async (params?: any) => {
  const res = await request(generateAPIPath(`api/v2/web-notification`), {
    method: "GET",
    params: {
      ...params,
      order_by: "created_at desc",
      or_filters: JSON.stringify([
        ["iot_notification", "customer_id", "=", getCustomerIdFromToken()],
        ["iot_notification", "customer_user", "=", getUserIdFromToken()],
      ]),
      filters: JSON.stringify([...(params?.filters || [])]),
    },
  });
  return (res.responseData as PaginationResponseResult<NoticeDataRes[]>)?.result
    ?.data;
};

export class IIotNotification {
  name!: string | number;
  customer_user?: string; // Data
  message?: string; // Data
  created_at?: string; // Datetime
  entity?: string; // Data
  type?: string; // Data
  is_read?: string | boolean; // Check
}

export async function updateWebNotification(notification: IIotNotification) {
  const result = await request(generateAPIPath(`api/v2/web-notification`), {
    method: "PUT",
    data: notification,
  });
  return result;
}
