import {
  SchedulePlanProps,
  ScheduleProgram,
} from "../../stores/schedulePlanStore";
import { request } from "../request";
import { generateAPIPath } from "../utilities";

// export const getsched = async () => {
//   return request(generateAPIPath("api/sche"), {
//     method: "GET",
//     headers: {
//       "Content-Type": "application/json",
//     },
//   });
// };

export interface ResultData {
  result: {
    data: SchedulePlanProps[];
  };
}

export const getSchedulePlan = async (
  device_id: string
): Promise<ResultData> => {
  // const filters = [["iot_schedule_plan", "device_id", "like", device_id]];
  const filters = [
    [
      "iot_schedule_plan",
      "device_id",
      "like",
      // "84fc09e0-05f2-11f0-abdc-fd15fc57941d",
      device_id,
    ],
  ];

  const queryParams = new URLSearchParams({
    filters: JSON.stringify(filters),
  });
  const res = await request(
    generateAPIPath(`api/v2/schedulePlan?${queryParams.toString()}`),
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },

      // params: {
      //   filters: [["iot_schedule_plan", "device_id", "like", device_id]],
      // },
    }
  );
  return res.responseData;
};

export const createSchedulePlan = async (plan_to_create: {
  label: string;
  device_id: string;
  start_date: string;
  end_date: string;
  enable: number;
}) => {
  return request(generateAPIPath("api/v2/schedulePlan"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: plan_to_create,
  });
};

export const updateSchedulePlan = async (plan_to_update: {
  name: string;
  label: string;
  device_id: string;
  start_date: string;
  end_date: string;
  enable: number;
}) => {
  return request(generateAPIPath("api/v2/schedulePlan/ver2"), {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: plan_to_update,
  });
};

export const deleteSchedulePlan = async (plan_id: string) => {
  return request(generateAPIPath(`api/v2/schedulePlan?name=${plan_id}`), {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  });
};

export const createScheduleProgram = async (
  schedule_program: Partial<ScheduleProgram>
) => {
  return request(generateAPIPath("api/v2/schedule/ver2"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: schedule_program,
  });
};

export const updateScheduleProgram = async (
  schedule_program: Partial<ScheduleProgram>
) => {
  return request(generateAPIPath(`api/v2/schedule/ver2`), {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: schedule_program,
  });
};

export const deleteScheduleProgram = async (schedule_program_id: string) => {
  return request(
    generateAPIPath(`api/v2/schedule?name=${schedule_program_id}`),
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
};

// export const updateDeviceScheduleStatus = async (device_id: string, status: string) => {
//   return request(generateAPIPath("api/v2/schedulePlan"), {
//     method: "PUT",
//     headers: {
//       "Content-Type": "application/json",
//     },
//     data: {
//       device_id,
//       status,
//     },
//   });
// };
