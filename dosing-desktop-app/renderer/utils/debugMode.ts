// Debug mode configuration
export const DEBUG_CONFIG = {
  // Environment-based debug mode
  isDevelopment: process.env.NODE_ENV === 'development',
  
  // Manual debug mode (can be toggled)
  isDebugEnabled: false,
  
  // Feature flags for different debug components
  features: {
    calibrationDebugInfo: false,
    calibrationTestTool: false,
    apiLogger: false,
    consoleLogging: true,
  }
};

// Toggle debug mode
export const toggleDebugMode = () => {
  DEBUG_CONFIG.isDebugEnabled = !DEBUG_CONFIG.isDebugEnabled;
  console.log(`Debug mode: ${DEBUG_CONFIG.isDebugEnabled ? 'ON' : 'OFF'}`);
};

// Toggle specific debug feature
export const toggleDebugFeature = (feature: keyof typeof DEBUG_CONFIG.features) => {
  DEBUG_CONFIG.features[feature] = !DEBUG_CONFIG.features[feature];
  console.log(`Debug feature ${feature}: ${DEBUG_CONFIG.features[feature] ? 'ON' : 'OFF'}`);
};

// Check if debug should be shown
export const shouldShowDebug = () => {
  return DEBUG_CONFIG.isDevelopment || DEBUG_CONFIG.isDebugEnabled;
};

// Check specific debug feature
export const shouldShowDebugFeature = (feature: keyof typeof DEBUG_CONFIG.features) => {
  return shouldShowDebug() && DEBUG_CONFIG.features[feature];
};

// Debug logger
export const debugLog = (message: string, data?: any) => {
  if (shouldShowDebugFeature('consoleLogging')) {
    console.log(`[CALIBRATION_DEBUG] ${message}`, data || '');
  }
};

// Make debug functions available globally for console access
if (typeof window !== 'undefined') {
  (window as any).calibrationDebug = {
    toggle: toggleDebugMode,
    toggleFeature: toggleDebugFeature,
    config: DEBUG_CONFIG,
    log: debugLog,
  };
}
