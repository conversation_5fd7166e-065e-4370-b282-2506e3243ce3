import { ITokenInfo, RoleEnum } from "../types/auth.type";
import { jwtDecode } from "jwt-decode";

export const getAccessToken = () => {
  try {
    const info = JSON.parse(localStorage.getItem("token") || "{}");
    return (info.token as string) || null;
  } catch {
    return null;
  }
};
export const getInfoFromAccessToken = () => {
  try {
    const token = getAccessToken();
    const info = jwtDecode(token as string) as ITokenInfo;
    if (info.is_admin) {
      info.user_role.push(RoleEnum.CUSTOMER_ADMIN);
    }
    // return {
    //   ...info,
    //   // user_role:Object.keys(RoleEnum)
    //   // user_role: [RoleEnum.TECHNICIAN_EMPLOYEE],
    //   user_role:[...info.user_role,]
    // } as ITokenInfo;
    return info;
  } catch (error) {
    return null;
  }
};
export const getUserIdFromToken = () => {
  const info = getInfoFromAccessToken();
  return info?.user_id;
};
export const getCustomerIdFromToken = () => {
  const info = getInfoFromAccessToken();
  return info?.customer_id;
};
