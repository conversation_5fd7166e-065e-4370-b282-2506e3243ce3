/**
 * Calculate calibration factor based on actual and measured values
 */
export function calculateCalibrationFactor(
  actual: number,
  measured: number
): number {
  return actual / measured;
}

/**
 * Generate a calibration expression string
 */
export function generateCalibrationExpression(factor: number): string {
  return `y = ${factor.toFixed(4)} * x`;
}

/**
 * Apply calibration factor to a raw value
 */
export function applyCalibration(rawValue: number, factor: number): number {
  return rawValue * factor;
}

/**
 * Perform linear interpolation between calibration points
 * This is more advanced and would be used if you have multiple calibration points
 */
export function interpolateCalibration(
  rawValue: number,
  calibrationPoints: Array<{ measured: number; actual: number }>
): number {
  // Sort points by measured value
  const sortedPoints = [...calibrationPoints].sort(
    (a, b) => a.measured - b.measured
  );

  // If raw value is below the lowest calibration point
  if (rawValue <= sortedPoints[0].measured) {
    const factor = sortedPoints[0].actual / sortedPoints[0].measured;
    return rawValue * factor;
  }

  // If raw value is above the highest calibration point
  if (rawValue >= sortedPoints[sortedPoints.length - 1].measured) {
    const point = sortedPoints[sortedPoints.length - 1];
    const factor = point.actual / point.measured;
    return rawValue * factor;
  }

  // Find the two calibration points to interpolate between
  for (let i = 0; i < sortedPoints.length - 1; i++) {
    const lower = sortedPoints[i];
    const upper = sortedPoints[i + 1];

    if (rawValue >= lower.measured && rawValue <= upper.measured) {
      // Linear interpolation formula
      const proportion =
        (rawValue - lower.measured) / (upper.measured - lower.measured);
      return lower.actual + proportion * (upper.actual - lower.actual);
    }
  }

  // Fallback (should never reach here if input validation is correct)
  return rawValue;
}
